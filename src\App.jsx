import { useState } from 'react'
import './App.css'
import LiveTrainerSession from './components/LiveTrainerSession/LiveTrainerSession'

function App() {
  const [showSession, setShowSession] = useState(true)

  return (
    <div className="app">
      <header className="app-header">
        <h1>Gym Live Trainer Sessions</h1>
        <button
          onClick={() => setShowSession(!showSession)}
          className="toggle-session-btn"
        >
          {showSession ? 'Hide Session' : 'Show Session'}
        </button>
      </header>

      {showSession && (
        <main className="app-main">
          <LiveTrainerSession />
        </main>
      )}
    </div>
  )
}

export default App
