# Live Trainer Sessions UI Module

A comprehensive React-based UI module for managing live trainer sessions in a gym application. This module provides real-time communication, session timing, trainer profiles, and interactive messaging capabilities.

## Features

### 🕐 Session Timer Component
- **Real-time session tracking** with MM:SS or HH:MM:SS format
- **Start/Pause/Stop controls** for trainers
- **Visual progress indicator** with circular progress ring
- **Session state management** (active, paused, completed)
- **Target duration tracking** with remaining time display

### 👨‍💼 Trainer Profile Section
- **Trainer avatar** with online/offline status indicator
- **Credentials and certifications** display
- **Rating system** with star ratings and review count
- **Current session focus** and specialties
- **Quick action buttons** (Message, Call, Video)
- **Unread message notifications**

### 💬 Real-time Message Overlay
- **Live chat interface** for trainer-client communication
- **Message status indicators** (sending, delivered, read)
- **Quick response buttons** for common messages
- **Typing indicators** for both parties
- **Minimizable/expandable** overlay design
- **Connection status** monitoring
- **Auto-scroll** to latest messages

## Technical Implementation

### Architecture
```
LiveTrainerSession/
├── LiveTrainerSession.jsx          # Main container component
├── SessionTimer/                   # Timer functionality
├── TrainerProfile/                 # Trainer information display
├── MessageOverlay/                 # Real-time messaging
├── ProgressRing/                   # Circular progress indicator
└── README.md                       # This documentation
```

### Context Management
- **SessionContext**: Manages session state, timer, and trainer data
- **MessageContext**: Handles real-time messaging and WebSocket communication

### Custom Hooks
- **useTimer**: Session timer logic with start/pause/stop functionality
- **useWebSocket**: Mock WebSocket implementation for real-time features

### Styling
- **CSS Modules** for component-scoped styling
- **Responsive design** with mobile-first approach
- **High contrast** support for workout environments
- **Touch-friendly** controls (minimum 44px touch targets)
- **Accessibility** compliant with ARIA labels and keyboard navigation

## Usage

### Basic Implementation
```jsx
import LiveTrainerSession from './components/LiveTrainerSession/LiveTrainerSession';

function App() {
  return (
    <div className="app">
      <LiveTrainerSession />
    </div>
  );
}
```

### With Custom Configuration
```jsx
import { SessionProvider } from './contexts/SessionContext';
import { MessageProvider } from './contexts/MessageContext';

// Custom session configuration
const sessionConfig = {
  duration: 3600, // 1 hour
  type: 'strength-training',
  intensity: 'high'
};

// Custom trainer data
const trainerData = {
  name: 'John Doe',
  credentials: ['NASM-CPT', 'ACSM-EP'],
  rating: 4.8,
  specialties: ['HIIT', 'Strength Training']
};
```

## Component APIs

### SessionTimer Props
- `initialTime`: Starting time in seconds (default: 0 for count-up)
- `autoStart`: Whether to start automatically (default: false)
- `onComplete`: Callback when timer completes
- `onTick`: Callback on each second

### TrainerProfile Props
- `trainer`: Trainer object with name, avatar, credentials, etc.
- `onMessage`: Callback when message button is clicked
- `onCall`: Callback when call button is clicked
- `onVideo`: Callback when video button is clicked

### MessageOverlay Props
- `isVisible`: Controls overlay visibility
- `messages`: Array of message objects
- `onSendMessage`: Callback for sending messages
- `onClose`: Callback when overlay is closed

## Real-time Features

### WebSocket Integration
The module includes a mock WebSocket implementation that simulates:
- **Connection management** with reconnection logic
- **Message delivery** with status tracking
- **Typing indicators** for enhanced UX
- **Automatic trainer responses** for demonstration

### Message Types
```javascript
{
  id: 'unique-id',
  text: 'Message content',
  sender: 'trainer' | 'client',
  timestamp: Date,
  status: 'sending' | 'delivered' | 'read'
}
```

## Responsive Design

### Breakpoints
- **Desktop**: 1024px and above
- **Tablet**: 768px - 1023px
- **Mobile**: 480px - 767px
- **Small Mobile**: Below 480px

### Mobile Optimizations
- **Stacked layout** for session content
- **Full-width message overlay** on small screens
- **Touch-friendly** button sizing
- **Simplified navigation** for better mobile UX

## Accessibility Features

### ARIA Support
- **Proper labeling** for all interactive elements
- **Screen reader** friendly content structure
- **Keyboard navigation** support
- **Focus management** for modal overlays

### Visual Accessibility
- **High contrast** mode support
- **Reduced motion** preferences respected
- **Color-blind friendly** design choices
- **Scalable text** and UI elements

## Performance Optimizations

### React Optimizations
- **useCallback** and **useMemo** for expensive operations
- **Component memoization** where appropriate
- **Efficient re-rendering** with proper dependency arrays

### CSS Optimizations
- **CSS Modules** for scoped styling and better performance
- **Hardware acceleration** for animations
- **Optimized animations** using transform and opacity

## Browser Support

- **Chrome**: 88+
- **Firefox**: 85+
- **Safari**: 14+
- **Edge**: 88+
- **Mobile browsers**: iOS Safari 14+, Chrome Mobile 88+

## Future Enhancements

### Planned Features
- **Video call integration** with WebRTC
- **Screen sharing** for exercise demonstrations
- **Workout plan integration** with real-time updates
- **Biometric data** display (heart rate, calories)
- **Session recording** and playback
- **Multi-language support**

### Technical Improvements
- **Real WebSocket** implementation
- **Offline support** with service workers
- **Push notifications** for mobile devices
- **Advanced analytics** and reporting
- **Integration with fitness trackers**

## Development

### Running the Module
```bash
npm run dev
```

### Testing
```bash
npm run test
```

### Building
```bash
npm run build
```

## Dependencies

### Core Dependencies
- **React**: 19.1.0+
- **React DOM**: 19.1.0+

### Development Dependencies
- **Vite**: 7.0.4+
- **ESLint**: 9.30.1+

## License

This module is part of the gym application and follows the same licensing terms.
