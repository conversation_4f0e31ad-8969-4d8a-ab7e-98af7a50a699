.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.demo-instructions {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  overflow-y: auto;
}

.instructions-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.instructions-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
}

.close-instructions {
  width: 40px;
  height: 40px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-instructions:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.instructions-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.demo-modes {
  margin-bottom: 2rem;
}

.demo-modes h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.mode-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.mode-btn {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.mode-btn:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.1);
}

.mode-btn.active {
  border-color: #667eea;
  background: #f0f4ff;
}

.mode-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.mode-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.features-overview,
.test-scenarios {
  margin-bottom: 2rem;
}

.features-overview h3,
.test-scenarios h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.features-grid,
.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.feature-card,
.scenario-card {
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.feature-card h4,
.scenario-card h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.feature-card p {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.4;
}

.feature-card ul,
.scenario-card ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #374151;
  font-size: 0.875rem;
}

.feature-card li,
.scenario-card li {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.demo-session {
  min-height: 100vh;
  position: relative;
}

.demo-controls {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1500;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.75rem 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.show-instructions {
  padding: 0.5rem 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.show-instructions:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.mode-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mode-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.mode-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  padding: 0.25rem 0.5rem;
  background: #f3f4f6;
  border-radius: 6px;
}

/* Demo mode specific styles */
.demo-session.mobile {
  max-width: 375px;
  margin: 0 auto;
  border: 8px solid #1f2937;
  border-radius: 24px;
  overflow: hidden;
}

.demo-session.high-intensity .live-trainer-session {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.demo-session.beginner .live-trainer-session {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .demo-instructions {
    padding: 1rem;
  }
  
  .instructions-content {
    padding: 1.5rem;
  }
  
  .instructions-header h2 {
    font-size: 1.5rem;
  }
  
  .features-grid,
  .scenarios-grid {
    grid-template-columns: 1fr;
  }
  
  .mode-selector {
    grid-template-columns: 1fr;
  }
  
  .demo-controls {
    position: static;
    margin: 1rem;
    justify-content: center;
  }
  
  .demo-session.mobile {
    max-width: 100%;
    border: none;
    border-radius: 0;
  }
}

@media (max-width: 480px) {
  .demo-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .mode-indicator {
    flex-direction: column;
    gap: 0.25rem;
    text-align: center;
  }
}
