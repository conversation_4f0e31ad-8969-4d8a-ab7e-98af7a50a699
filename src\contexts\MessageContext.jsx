import { createContext, useContext, useState, useCallback } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';

const MessageContext = createContext();

export const useMessages = () => {
  const context = useContext(MessageContext);
  if (!context) {
    throw new Error('useMessages must be used within a MessageProvider');
  }
  return context;
};

export const MessageProvider = ({ children }) => {
  // WebSocket connection
  const webSocket = useWebSocket('ws://localhost:8080/trainer-session');
  
  // UI state
  const [isOverlayVisible, setIsOverlayVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // Quick response templates
  const [quickResponses] = useState([
    "👍 Got it!",
    "✋ Need a break",
    "💪 Ready for more",
    "❓ Can you repeat?",
    "😅 That's tough!",
    "🔥 Feeling great!",
    "💧 Need water",
    "⏰ How much time left?"
  ]);
  
  // Message input state
  const [messageInput, setMessageInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  
  // Show/hide overlay
  const showOverlay = useCallback(() => {
    setIsOverlayVisible(true);
    setIsMinimized(false);
    // Mark messages as read when overlay is opened
    webSocket.markAsRead();
    setUnreadCount(0);
  }, [webSocket]);
  
  const hideOverlay = useCallback(() => {
    setIsOverlayVisible(false);
  }, []);
  
  // Minimize/maximize overlay
  const minimizeOverlay = useCallback(() => {
    setIsMinimized(true);
  }, []);
  
  const maximizeOverlay = useCallback(() => {
    setIsMinimized(false);
  }, []);
  
  // Toggle overlay visibility
  const toggleOverlay = useCallback(() => {
    if (isOverlayVisible) {
      if (isMinimized) {
        maximizeOverlay();
      } else {
        hideOverlay();
      }
    } else {
      showOverlay();
    }
  }, [isOverlayVisible, isMinimized, showOverlay, hideOverlay, maximizeOverlay]);
  
  // Send message
  const sendMessage = useCallback((text) => {
    if (!text.trim()) return;
    
    webSocket.sendMessage(text.trim());
    setMessageInput('');
    setIsTyping(false);
  }, [webSocket]);
  
  // Send quick response
  const sendQuickResponse = useCallback((response) => {
    webSocket.sendMessage(response);
  }, [webSocket]);
  
  // Handle message input change
  const handleInputChange = useCallback((value) => {
    setMessageInput(value);
    
    // Simulate typing indicator
    if (value.length > 0 && !isTyping) {
      setIsTyping(true);
      webSocket.simulateTyping();
    } else if (value.length === 0) {
      setIsTyping(false);
    }
  }, [isTyping, webSocket]);
  
  // Update unread count when new messages arrive
  useEffect(() => {
    const trainerMessages = webSocket.messages.filter(
      msg => msg.sender === 'trainer' && msg.status !== 'read'
    );
    setUnreadCount(trainerMessages.length);
  }, [webSocket.messages]);
  
  // Get latest message
  const getLatestMessage = useCallback(() => {
    const messages = webSocket.messages;
    return messages.length > 0 ? messages[messages.length - 1] : null;
  }, [webSocket.messages]);
  
  // Get messages from trainer only
  const getTrainerMessages = useCallback(() => {
    return webSocket.messages.filter(msg => msg.sender === 'trainer');
  }, [webSocket.messages]);
  
  // Get messages from client only
  const getClientMessages = useCallback(() => {
    return webSocket.messages.filter(msg => msg.sender === 'client');
  }, [webSocket.messages]);
  
  // Clear all messages
  const clearAllMessages = useCallback(() => {
    webSocket.clearMessages();
    setUnreadCount(0);
  }, [webSocket]);
  
  // Message notification handler
  const showNotification = useCallback((message) => {
    if (!isOverlayVisible && message.sender === 'trainer') {
      // In a real app, this would trigger a browser notification
      console.log('New message notification:', message.text);
      setUnreadCount(prev => prev + 1);
    }
  }, [isOverlayVisible]);
  
  // Auto-show overlay on first trainer message
  useEffect(() => {
    const trainerMessages = getTrainerMessages();
    if (trainerMessages.length === 1 && !isOverlayVisible) {
      // Auto-show overlay for first trainer message
      setTimeout(() => {
        showOverlay();
      }, 1000);
    }
  }, [webSocket.messages, isOverlayVisible, getTrainerMessages, showOverlay]);
  
  const value = {
    // WebSocket state
    isConnected: webSocket.isConnected,
    connectionStatus: webSocket.connectionStatus,
    messages: webSocket.messages,
    isTrainerTyping: webSocket.isTyping,
    
    // UI state
    isOverlayVisible,
    isMinimized,
    unreadCount,
    messageInput,
    isTyping,
    quickResponses,
    
    // Actions
    showOverlay,
    hideOverlay,
    minimizeOverlay,
    maximizeOverlay,
    toggleOverlay,
    sendMessage,
    sendQuickResponse,
    handleInputChange,
    clearAllMessages,
    
    // Computed
    getLatestMessage,
    getTrainerMessages,
    getClientMessages,
    
    // WebSocket actions
    reconnect: webSocket.reconnect
  };
  
  return (
    <MessageContext.Provider value={value}>
      {children}
    </MessageContext.Provider>
  );
};
