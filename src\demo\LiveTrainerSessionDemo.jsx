import { useState } from 'react';
import LiveTrainerSession from '../components/LiveTrainerSession/LiveTrainerSession';
import './LiveTrainerSessionDemo.css';

/**
 * Demo component to showcase Live Trainer Session features
 * This component provides controls to test different scenarios
 */
const LiveTrainerSessionDemo = () => {
  const [demoMode, setDemoMode] = useState('default');
  const [showInstructions, setShowInstructions] = useState(true);

  const demoModes = [
    {
      id: 'default',
      name: 'Default Session',
      description: 'Standard live training session with all features enabled'
    },
    {
      id: 'mobile',
      name: 'Mobile View',
      description: 'Optimized layout for mobile devices'
    },
    {
      id: 'high-intensity',
      name: 'High Intensity',
      description: 'High-intensity workout session with different styling'
    },
    {
      id: 'beginner',
      name: 'Beginner Session',
      description: 'Beginner-friendly session with extra guidance'
    }
  ];

  const features = [
    {
      title: '⏱️ Session Timer',
      description: 'Real-time session tracking with start/pause/stop controls',
      actions: ['Click Start to begin the session', 'Use Pause to take breaks', 'Stop to end the session']
    },
    {
      title: '👨‍💼 Trainer Profile',
      description: 'Complete trainer information with credentials and ratings',
      actions: ['View trainer credentials', 'Check current session focus', 'Access quick communication options']
    },
    {
      title: '💬 Live Messaging',
      description: 'Real-time chat with typing indicators and quick responses',
      actions: ['Click Message button to open chat', 'Try quick response buttons', 'Send custom messages']
    },
    {
      title: '📱 Responsive Design',
      description: 'Optimized for all device sizes and orientations',
      actions: ['Resize browser window', 'Test on mobile devices', 'Check touch interactions']
    }
  ];

  const testScenarios = [
    {
      title: 'Start a Session',
      steps: [
        '1. Click the "Start" button in the Session Timer',
        '2. Watch the timer begin counting up',
        '3. Notice the live indicator in the header',
        '4. Observe the session status changes'
      ]
    },
    {
      title: 'Test Messaging',
      steps: [
        '1. Click the "Message" button in Trainer Profile',
        '2. Wait for automatic trainer messages',
        '3. Try the quick response buttons',
        '4. Send a custom message',
        '5. Notice typing indicators and message status'
      ]
    },
    {
      title: 'Mobile Experience',
      steps: [
        '1. Resize browser to mobile width (< 768px)',
        '2. Notice the responsive layout changes',
        '3. Test touch interactions',
        '4. Check message overlay behavior'
      ]
    }
  ];

  return (
    <div className="demo-container">
      {showInstructions && (
        <div className="demo-instructions">
          <div className="instructions-header">
            <h2>🏋️ Live Trainer Session Demo</h2>
            <button 
              onClick={() => setShowInstructions(false)}
              className="close-instructions"
              aria-label="Close instructions"
            >
              ×
            </button>
          </div>
          
          <div className="instructions-content">
            <div className="demo-modes">
              <h3>Demo Modes</h3>
              <div className="mode-selector">
                {demoModes.map(mode => (
                  <button
                    key={mode.id}
                    onClick={() => setDemoMode(mode.id)}
                    className={`mode-btn ${demoMode === mode.id ? 'active' : ''}`}
                  >
                    <div className="mode-name">{mode.name}</div>
                    <div className="mode-description">{mode.description}</div>
                  </button>
                ))}
              </div>
            </div>

            <div className="features-overview">
              <h3>Key Features</h3>
              <div className="features-grid">
                {features.map((feature, index) => (
                  <div key={index} className="feature-card">
                    <h4>{feature.title}</h4>
                    <p>{feature.description}</p>
                    <ul>
                      {feature.actions.map((action, actionIndex) => (
                        <li key={actionIndex}>{action}</li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>

            <div className="test-scenarios">
              <h3>Test Scenarios</h3>
              <div className="scenarios-grid">
                {testScenarios.map((scenario, index) => (
                  <div key={index} className="scenario-card">
                    <h4>{scenario.title}</h4>
                    <ol>
                      {scenario.steps.map((step, stepIndex) => (
                        <li key={stepIndex}>{step}</li>
                      ))}
                    </ol>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className={`demo-session ${demoMode}`}>
        <div className="demo-controls">
          <button 
            onClick={() => setShowInstructions(true)}
            className="show-instructions"
          >
            📖 Show Instructions
          </button>
          
          <div className="mode-indicator">
            <span className="mode-label">Mode:</span>
            <span className="mode-value">
              {demoModes.find(m => m.id === demoMode)?.name}
            </span>
          </div>
        </div>

        <LiveTrainerSession />
      </div>
    </div>
  );
};

export default LiveTrainerSessionDemo;
